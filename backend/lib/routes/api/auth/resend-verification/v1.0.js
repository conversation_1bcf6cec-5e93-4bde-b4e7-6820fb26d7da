const _ = require('lodash');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const UserModel = require('../../../../models/user');

module.exports = async (req, res) => {
  try {
    const { email } = req.body;

    // Check params
    if (!email) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Email is required'
      });
    }

    // Find user by email
    const user = await UserModel.findOne({
      email,
      status: 1
    });

    if (!user) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'User not found'
      });
    }

    if (user.isEmailVerified) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Email is already verified'
      });
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const verificationTokenExpiry = new Date(Date.now() + 86400000); // 24 hours

    // Save verification token to user
    await UserModel.updateOne(
      { _id: user._id },
      {
        emailVerificationToken: verificationToken,
        emailVerificationExpires: verificationTokenExpiry,
        updatedAt: Date.now()
      }
    );

    // Send email if email service is configured
    try {
      const emailConfig = config.get('emailInfos')[0];
      if (emailConfig && emailConfig.auth && emailConfig.auth.user) {
        const transporter = nodemailer.createTransporter({
          service: emailConfig.service,
          auth: emailConfig.auth
        });

        const verificationUrl = `http://localhost:3001/verify-email?token=${verificationToken}`;

        const mailOptions = {
          from: emailConfig.auth.user,
          to: email,
          subject: 'Email Verification - TeleStore',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2563eb;">Welcome to TeleStore!</h2>
              <p>Thank you for registering with TeleStore. Please verify your email address to complete your registration.</p>
              <p>Click the link below to verify your email:</p>
              <a href="${verificationUrl}" style="display: inline-block; padding: 12px 24px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 6px; margin: 16px 0;">Verify Email</a>
              <p>This link will expire in 24 hours.</p>
              <p>If you didn't create this account, please ignore this email.</p>
              <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">TeleStore - Your secure file storage solution</p>
            </div>
          `
        };

        await transporter.sendMail(mailOptions);
        console.log('Verification email sent to:', email);
      } else {
        console.log('Email service not configured, verification token generated but not sent');
      }
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the request if email fails
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Verification email sent successfully.'
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
