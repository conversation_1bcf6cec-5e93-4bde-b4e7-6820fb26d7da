const _ = require('lodash');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const { token } = req.query;

    // Check params
    if (!token) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Verification token is required'
      });
    }

    // Find user with valid verification token
    const user = await UserModel.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: new Date() },
      status: 1
    });

    if (!user) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Invalid or expired verification token'
      });
    }

    // Update user as verified and clear verification token
    await UserModel.updateOne(
      { _id: user._id },
      {
        isEmailVerified: true,
        emailVerificationToken: undefined,
        emailVerificationExpires: undefined,
        updatedAt: Date.now()
      }
    );

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Email verified successfully. You can now login.'
    });

  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
