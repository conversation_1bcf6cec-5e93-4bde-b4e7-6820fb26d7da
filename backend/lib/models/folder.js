const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const FolderSchema = new mongoose.Schema(
  {
    folderName: {
      type: String,
      required: true
    },
    parentId: {
      type: Schema.Types.ObjectId,
      ref: 'Folder',
      default: null,
      index: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true
    },
    deletedAt: {
      type: Date,
      default: null
    },
    ownerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null,
      index: true
    }
  },
  { 
    id: false, 
    versionKey: false,
    timestamps: false
  }
)

// Indexes for better query performance
FolderSchema.index({ parentId: 1, isDeleted: 1 })
FolderSchema.index({ folderName: 1, isDeleted: 1 })
FolderSchema.index({ createdAt: -1 })
FolderSchema.index({ ownerId: 1, isDeleted: 1 })

// Compound index for unique folder names within same parent
FolderSchema.index({ folderName: 1, parentId: 1, isDeleted: 1 }, { unique: true })

module.exports = mongoConnections("master").model("Folder", FolderSchema)
