import React, { useState } from 'react';
import './EmailVerificationBanner.css';

interface EmailVerificationBannerProps {
  user: any;
  onResendVerification: () => void;
}

const EmailVerificationBanner: React.FC<EmailVerificationBannerProps> = ({ 
  user, 
  onResendVerification 
}) => {
  const [isResending, setIsResending] = useState(false);
  const [message, setMessage] = useState('');

  // Only show for local users who haven't verified email
  if (!user || user.provider !== 'local' || user.isEmailVerified) {
    return null;
  }

  const handleResend = async () => {
    setIsResending(true);
    setMessage('');

    try {
      const response = await fetch('/api/v1.0/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: user.email }),
      });

      const data = await response.json();

      if (data.code === 200) {
        setMessage('Verification email sent successfully!');
      } else {
        setMessage(data.message || 'Failed to send verification email');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="email-verification-banner">
      <div className="banner-content">
        <div className="banner-icon">📧</div>
        <div className="banner-text">
          <strong>Email Verification Required</strong>
          <p>Please check your email and click the verification link to activate your account.</p>
          {message && (
            <p className={`banner-message ${message.includes('successfully') ? 'success' : 'error'}`}>
              {message}
            </p>
          )}
        </div>
        <button
          className="resend-button"
          onClick={handleResend}
          disabled={isResending}
        >
          {isResending ? 'Sending...' : 'Resend Email'}
        </button>
      </div>
    </div>
  );
};

export default EmailVerificationBanner;
