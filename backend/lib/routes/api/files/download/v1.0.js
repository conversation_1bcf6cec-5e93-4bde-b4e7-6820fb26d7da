const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false
    }).lean();

    if (!file) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found'
      });
    }

    // Get file URL from Telegram
    const cacheKey = cacheService.getTelegramUrlKey(file.telegramFileId);
    let fileUrl = await cacheService.get(cacheKey);

    if (!fileUrl) {
      // Get fresh URL from Telegram
      fileUrl = await telegramService.getFileUrl(file.telegramFileId);

      // Cache URL for 30 minutes (Telegram URLs expire after ~1 hour)
      try {
        await cacheService.set(cacheKey, fileUrl, 1800);
      } catch (cacheError) {
        console.warn('Cache set failed:', cacheError.message);
      }
    }

    // Set response headers
    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.originalFileName)}"`);
    res.setHeader('Content-Length', file.fileSize);

    // Get file stream from Telegram
    const fileStream = await telegramService.getFileStream(file.telegramFileId);

    // Pipe the stream to response
    fileStream.pipe(res);

    // Handle stream events
    fileStream.on('error', (error) => {
      console.error('Stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Failed to download file'
        });
      }
    });

    fileStream.on('end', () => {
      console.log(`File downloaded: ${file.originalFileName}, ID: ${file._id}`);
    });

  } catch (error) {
    console.error('Download error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to download file'
      });
    }
  }
};
