export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const getFileIcon = (mimeType: string): string => {
  if (mimeType.startsWith('image/')) {
    return '🖼️';
  } else if (mimeType.startsWith('video/')) {
    return '🎥';
  } else if (mimeType.startsWith('audio/')) {
    return '🎵';
  } else if (mimeType.includes('pdf')) {
    return '📄';
  } else if (mimeType.includes('document') || mimeType.includes('word')) {
    return '📝';
  } else if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) {
    return '📊';
  } else if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) {
    return '📽️';
  } else if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) {
    return '📦';
  }
  return '📄';
};

export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith('image/');
};

export const isVideoFile = (mimeType: string): boolean => {
  return mimeType.startsWith('video/');
};

export const isAudioFile = (mimeType: string): boolean => {
  return mimeType.startsWith('audio/');
};

export const isPdfFile = (mimeType: string): boolean => {
  return mimeType === 'application/pdf';
};

export const isTextFile = (mimeType: string): boolean => {
  return mimeType.startsWith('text/') ||
         mimeType.includes('json') ||
         mimeType.includes('javascript') ||
         mimeType.includes('css') ||
         mimeType.includes('xml') ||
         mimeType.includes('yaml') ||
         mimeType.includes('yml');
};

export const isOfficeFile = (mimeType: string): boolean => {
  return mimeType.includes('document') ||
         mimeType.includes('word') ||
         mimeType.includes('spreadsheet') ||
         mimeType.includes('excel') ||
         mimeType.includes('presentation') ||
         mimeType.includes('powerpoint') ||
         mimeType.includes('officedocument');
};

export const isArchiveFile = (mimeType: string): boolean => {
  return mimeType.includes('zip') ||
         mimeType.includes('rar') ||
         mimeType.includes('tar') ||
         mimeType.includes('gzip') ||
         mimeType.includes('7z') ||
         mimeType.includes('archive');
};

export const getFileCategory = (mimeType: string): string => {
  if (isImageFile(mimeType)) return 'image';
  if (isVideoFile(mimeType)) return 'video';
  if (isAudioFile(mimeType)) return 'audio';
  if (isPdfFile(mimeType)) return 'pdf';
  if (isTextFile(mimeType)) return 'text';
  if (isOfficeFile(mimeType)) return 'office';
  if (isArchiveFile(mimeType)) return 'archive';
  return 'other';
};

export const truncateFileName = (fileName: string, maxLength: number = 30): string => {
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop();
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

  if (extension) {
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
    return `${truncatedName}.${extension}`;
  }

  return fileName.substring(0, maxLength - 3) + '...';
};

export const validateFileName = (fileName: string): boolean => {
  const invalidChars = /[<>:"/\\|?*]/;
  return !invalidChars.test(fileName) && fileName.trim().length > 0;
};

export const downloadFile = (url: string, fileName: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
