const _ = require('lodash');
const bcrypt = require('bcryptjs');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const { token, password } = req.body;

    // Check params
    if (!token || !password) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Token and password are required'
      });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Password must be at least 6 characters long'
      });
    }

    // Find user with valid reset token
    const user = await UserModel.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: new Date() },
      status: 1
    });

    if (!user) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Invalid or expired reset token'
      });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update user password and clear reset token
    await UserModel.updateOne(
      { _id: user._id },
      {
        password: hashedPassword,
        passwordResetToken: undefined,
        passwordResetExpires: undefined,
        updatedAt: Date.now()
      }
    );

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Password reset successfully. You can now login with your new password.'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
