.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #333;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  text-align: center;
}

.login-tabs {
  display: flex;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e5e9;
}

.tab-button {
  flex: 1;
  padding: 12px;
  border: none;
  background: #f8f9fa;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tab-button:hover:not(.active) {
  background: #e9ecef;
}

.login-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.oauth-section {
  margin-top: 30px;
}

.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.divider span {
  background: white;
  padding: 0 15px;
  color: #666;
  font-size: 0.9rem;
}

.oauth-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.oauth-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.oauth-button:hover:not(:disabled) {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.oauth-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.oauth-icon {
  width: 20px;
  height: 20px;
}

.google-button:hover:not(:disabled) {
  border-color: #4285F4;
}

.telegram-login-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

#telegram-login-container {
  display: flex;
  justify-content: center;
}

/* Telegram widget styling override */
#telegram-login-container iframe {
  border-radius: 8px !important;
  border: 2px solid #e1e5e9 !important;
  transition: all 0.3s ease !important;
}

#telegram-login-container iframe:hover {
  border-color: #0088cc !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .login-container {
    background: #1a1a1a;
    color: #fff;
  }
  
  .login-header h1 {
    color: #fff;
  }
  
  .form-group input {
    background: #2a2a2a;
    border-color: #444;
    color: #fff;
  }
  
  .form-group input:focus {
    border-color: #667eea;
  }
  
  .tab-button {
    background: #2a2a2a;
    color: #ccc;
  }
  
  .tab-button:hover:not(.active) {
    background: #333;
  }
  
  .oauth-button {
    background: #2a2a2a;
    border-color: #444;
    color: #fff;
  }
  
  .oauth-button:hover:not(:disabled) {
    border-color: #667eea;
  }
  
  .divider::before {
    background: #444;
  }
  
  .divider span {
    background: #1a1a1a;
    color: #ccc;
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .login-page {
    padding: 10px;
  }
  
  .login-container {
    padding: 30px 20px;
  }
  
  .login-header h1 {
    font-size: 2rem;
  }
}
