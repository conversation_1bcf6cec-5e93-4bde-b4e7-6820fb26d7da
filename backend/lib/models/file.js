const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const FileSchema = new mongoose.Schema(
  {
    telegramFileId: {
      type: String,
      required: true,
      index: true
    },
    originalFileName: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    mimeType: {
      type: String,
      required: true
    },
    uploadDate: {
      type: Date,
      default: Date.now
    },
    parentId: {
      type: Schema.Types.ObjectId,
      ref: 'Folder',
      default: null,
      index: true
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true
    },
    deletedAt: {
      type: Date,
      default: null
    },
    ownerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null,
      index: true
    },
    isProcessingAI: {
      type: Boolean,
      default: false
    },
    aiMetadata: {
      type: Schema.Types.Mixed,
      default: {}
    }
  },
  { 
    id: false, 
    versionKey: false,
    timestamps: false
  }
)

// Indexes for better query performance
FileSchema.index({ parentId: 1, isDeleted: 1 })
FileSchema.index({ originalFileName: 1, isDeleted: 1 })
FileSchema.index({ uploadDate: -1 })
FileSchema.index({ ownerId: 1, isDeleted: 1 })

module.exports = mongoConnections("master").model("File", FileSchema)
