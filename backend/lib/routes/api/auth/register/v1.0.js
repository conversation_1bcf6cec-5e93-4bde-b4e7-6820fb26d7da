const _ = require('lodash');
const bcrypt = require('bcryptjs');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const { username, password, email, name, phone } = req.body;

    // Check params
    if (!username || !password || !email || !name) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Check existing user
    const existingUser = await UserModel.findOne({
      $or: [
        { username },
        { email }
      ]
    }).lean();

    if (existingUser) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.EXISTS
      });
    }

    // Create user
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = await UserModel.create({
      username,
      password: hashedPassword,
      email,
      name,
      phone,
      role: 'user',
      status: 1,
      provider: 'local',
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Send response
    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.USER.CREATE_SUCCESS,
      data: {
        id: newUser._id,
        username: newUser.username,
        email: newUser.email,
        name: newUser.name
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
