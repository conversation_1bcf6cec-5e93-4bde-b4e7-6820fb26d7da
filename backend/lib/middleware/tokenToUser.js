const _ = require('lodash')
const redisConnections = require('../connections/redis')
const CONSTANTS = require('../const');
const MESSAGES = require('../message');

module.exports = async (req, res, next) => {
    let token = _.get(req, 'headers.token', '');

    if (!token) {
        return res.json({
            code: CONSTANTS.CODE.TOKEN_EXPIRE,
            message: MESSAGES.USER.TOKEN_EXPIRE
        });
    }

    console.log('🔍 TokenToUser middleware - Token:', token.substring(0, 20) + '...');

    try {
        const redis = redisConnections('master').getConnection();

        // Redis v4+ uses async/await, not callbacks
        const result = await redis.get(`user:${token}`);

        console.log('📡 Redis response - Result:', result ? 'Found' : 'Not found');

        if (!result) {
            console.log('❌ Token not found in Redis');
            return res.json({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: MESSAGES.USER.TOKEN_EXPIRE
            });
        }

        const objSign = JSON.parse(result);
        console.log('✅ Parsed user data:', objSign);

        if (!_.has(objSign, 'id')) {
            console.log('❌ No user ID in token data');
            return res.json({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: MESSAGES.USER.TOKEN_EXPIRE
            });
        }

        req.user = objSign;
        console.log('✅ User set in request:', req.user);
        next();

    } catch (error) {
        console.log('❌ Redis/Parse error:', error.message);
        return res.json({
            code: CONSTANTS.CODE.TOKEN_EXPIRE,
            message: MESSAGES.USER.TOKEN_EXPIRE
        });
    }
}
