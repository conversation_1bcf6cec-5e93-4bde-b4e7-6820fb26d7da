const _ = require('lodash');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const config = require('config');
const redisConnection = require('../../../../connections/redis');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const UserModel = require('../../../../models/user');

module.exports = async (req, res) => {
  try {
    const { username, password } = req.body;

    // Check params
    if (!username || !password) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Find user
    const user = await UserModel.findOne({
      username,
      status: 1
    }).lean();

    if (!user) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_EXISTS
      });
    }

    // Check if user is OAuth user without password
    if (!user.password && user.provider !== 'local') {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Please use OAuth login for this account'
      });
    }

    // Check if email is verified for local users
    if (user.provider === 'local' && !user.isEmailVerified) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Please verify your email before logging in. Check your email for verification link.'
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);

    if (!isMatch) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.INCORRECT_PASSWORD
      });
    }

    // Create new token
    const token = jwt.sign({ username, id: user._id }, config.secretKey);

    // Store in Redis using async API
    const userId = user._id.toString();
    const objSign = {
      id: userId,
      role: user.role,
    };

    try {
      const redis = redisConnection('master').getConnection();

      // Redis v4+ uses async/await
      await redis.set(`user:${userId}`, token);
      await redis.set(`user:${token}`, JSON.stringify(objSign));

      console.log('✅ Token stored in Redis successfully');
    } catch (redisError) {
      console.error('Redis storage error:', redisError);
      // Continue anyway - token will still work for this session
    }

    // Send response
    const responseData = _.merge({}, user, { token });
    _.unset(responseData, 'password');

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: responseData,
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
