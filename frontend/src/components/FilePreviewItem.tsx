import React from 'react';
import {
  Box,
  Typography,
  IconButton,
  Card,
  CardContent,
  useTheme,
} from '@mui/material';
import {
  Close as CloseIcon,
  InsertDriveFile as FileIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
  AudioFile as AudioIcon,
  PictureAsPdf as PdfIcon,
  Description as DocIcon,
} from '@mui/icons-material';

interface FilePreviewItemProps {
  file: File;
  onRemove: () => void;
  disabled?: boolean;
}

const FilePreviewItem: React.FC<FilePreviewItemProps> = ({ file, onRemove, disabled = false }) => {
  const theme = useTheme();

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <ImageIcon />;
    if (mimeType.startsWith('video/')) return <VideoIcon />;
    if (mimeType.startsWith('audio/')) return <AudioIcon />;
    if (mimeType === 'application/pdf') return <PdfIcon />;
    if (mimeType.includes('document') || mimeType.includes('text')) return <DocIcon />;
    return <FileIcon />;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getPreviewContent = () => {
    if (file.type.startsWith('image/')) {
      const imageUrl = URL.createObjectURL(file);
      return (
        <Box
          sx={{
            width: 60,
            height: 60,
            borderRadius: 1,
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'grey.100',
          }}
        >
          <img
            src={imageUrl}
            alt={file.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            onLoad={() => URL.revokeObjectURL(imageUrl)}
          />
        </Box>
      );
    }

    return (
      <Box
        sx={{
          width: 60,
          height: 60,
          borderRadius: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: theme.palette.mode === 'dark' ? 'grey.800' : 'grey.100',
          color: theme.palette.mode === 'dark' ? 'grey.300' : 'grey.600',
        }}
      >
        {getFileIcon(file.type)}
      </Box>
    );
  };

  return (
    <Card
      sx={{
        display: 'flex',
        alignItems: 'center',
        p: 1,
        mb: 1,
        position: 'relative',
        bgcolor: theme.palette.mode === 'dark' ? 'grey.900' : 'grey.50',
        border: `1px solid ${theme.palette.mode === 'dark' ? 'grey.700' : 'grey.200'}`,
        '&:hover': {
          bgcolor: theme.palette.mode === 'dark' ? 'grey.800' : 'grey.100',
        },
      }}
    >
      {getPreviewContent()}

      <CardContent sx={{ flex: 1, p: '8px 12px !important' }}>
        <Typography
          variant="body2"
          sx={{
            fontWeight: 500,
            mb: 0.5,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '200px',
          }}
        >
          {file.name}
        </Typography>
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ display: 'block' }}
        >
          {formatFileSize(file.size)}
        </Typography>
      </CardContent>

      <IconButton
        onClick={onRemove}
        disabled={disabled}
        size="small"
        sx={{
          position: 'absolute',
          top: 4,
          right: 4,
          bgcolor: disabled ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.6)',
          color: 'white',
          width: 24,
          height: 24,
          '&:hover': {
            bgcolor: disabled ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.8)',
          },
          '&:disabled': {
            color: 'rgba(255, 255, 255, 0.5)',
          },
        }}
      >
        <CloseIcon sx={{ fontSize: 16 }} />
      </IconButton>
    </Card>
  );
};

export default FilePreviewItem;
